<?php

namespace common\library\ai_agent;

use Generator;

class AiAgentProcessResponse
{

    public $answer = '';

    public int $recordId = 0;

    public int $historyId = 0;

    public string $function = '';

    public bool $stream = true;

    public int $messageType = 0;

    public array $context = [];

    // 是否保存AI历史对话记录
    public bool $needSave = true;

    public int $loading = 0;

    public int $status = AiAgentConstants::MESSAGE_STATUS_PROCESSING;

    public bool $initSkeletonMessage = true;

    // 流式输出
    public ?Generator $generator = null;

    public function setMessageType($messageType)
    {
        $this->messageType = $messageType;
    }

    public function setAnswer($answer)
    {
        $this->answer = $answer;
    }

    public function setStatus($status)
    {
        $this->status = $status;
    }

    public function setInitSkeletonMessage($initSkeletonMessage)
    {
        $this->initSkeletonMessage = $initSkeletonMessage;
    }

    public function setStream($stream)
    {
        $this->stream = $stream;
    }

    public function setLoading($loading)
    {
        $this->loading = $loading;
    }

    public function setRecordId($recordId)
    {
        $this->recordId = $recordId;
    }

    public function setHistoryId($historyId)
    {
        $this->historyId = $historyId;
    }

    public function setFunction($function)
    {
        $this->function = $function;
    }

    public function setGenerator(?Generator $generator): self
    {
        $this->generator = $generator;
        return $this;
    }

    public function getGenerator(): ?Generator
    {
        return $this->generator;
    }

    public function setNeedSave(bool $needSave)
    {
        $this->needSave = $needSave;
    }

    public function toArray(): array
    {
        return [
            'answer' => $this->answer,
            'recordId' => $this->recordId,
            'historyId' => $this->historyId,
            'messageType' => $this->messageType,
            'context' => $this->context
        ];
    }

}