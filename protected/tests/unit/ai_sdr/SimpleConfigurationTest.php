<?php

namespace tests\unit\ai_sdr;

/**
 * 简化的配置验证测试
 * 
 * 验证AI SDR测试环境的基础配置
 */
class SimpleConfigurationTest extends \ProjectTestCase
{
    /**
     * 测试基础配置
     */
    public function testBasicConfiguration()
    {
        // 验证常量定义
        $this->assertEquals(0, \common\library\ai_sdr\Constant::AI_SDR_TASK_STATUS_DRAFT);
        $this->assertEquals(1, \common\library\ai_sdr\Constant::AI_SDR_TASK_STATUS_PROCESSING);
        $this->assertEquals(2, \common\library\ai_sdr\Constant::AI_SDR_TASK_STATUS_PAUSED);
        $this->assertEquals(3, \common\library\ai_sdr\Constant::AI_SDR_TASK_STATUS_FINISHED);
        
        // 验证阶段常量
        $this->assertEquals(0, \common\library\ai_sdr\Constant::AI_SDR_STAGE_DIG);
        $this->assertEquals(1, \common\library\ai_sdr\Constant::AI_SDR_STAGE_REACHABLE);
        $this->assertEquals(2, \common\library\ai_sdr\Constant::AI_SDR_STAGE_MARKETING);
        $this->assertEquals(3, \common\library\ai_sdr\Constant::AI_SDR_STAGE_EFFECTIVE);
        $this->assertEquals(4, \common\library\ai_sdr\Constant::AI_SDR_STAGE_HIGHVALUE);
        
        $this->assertTrue(true, 'Constants are properly defined');
    }
    
    /**
     * 测试数据工厂
     */
    public function testDataFactory()
    {
        // 重置序列号
        AiSdrTestDataFactory::resetSequence();
        
        // 测试任务数据创建
        $taskData = AiSdrTestDataFactory::createTaskData();
        $this->assertIsArray($taskData);
        $this->assertArrayHasKey('client_id', $taskData);
        $this->assertArrayHasKey('user_id', $taskData);
        $this->assertEquals(999999, $taskData['client_id']);
        
        // 测试客户档案数据创建
        $clientProfile = AiSdrTestDataFactory::createClientProfileData();
        $this->assertIsArray($clientProfile);
        $this->assertArrayHasKey('company_name', $clientProfile);
        $this->assertArrayHasKey('main_products', $clientProfile);
        
        // 测试买家档案数据创建
        $buyerProfile = AiSdrTestDataFactory::createBuyerProfileData();
        $this->assertIsArray($buyerProfile);
        $this->assertArrayHasKey('company_name', $buyerProfile);
        $this->assertArrayHasKey('public_homepage', $buyerProfile);
        
        $this->assertTrue(true, 'Data factory is working correctly');
    }
    
    /**
     * 测试Mock服务
     */
    public function testMockServices()
    {
        // 测试Mock时间服务
        $timeService = MockServices::createMockTimeService('2024-01-01 10:00:00');
        $this->assertEquals('2024-01-01 10:00:00', $timeService->now());
        $this->assertEquals(strtotime('2024-01-01 10:00:00'), $timeService->timestamp());
        
        // 测试Mock缓存服务
        $cacheService = MockServices::createMockCacheService();
        $cacheService->set('test_key', 'test_value');
        $this->assertEquals('test_value', $cacheService->get('test_key'));
        $this->assertTrue($cacheService->wasKeyAccessed('test_key'));
        
        // 测试Mock队列服务
        $queueService = MockServices::createMockQueueService();
        $mockJob = new \stdClass();
        $queueService->dispatch($mockJob);
        $this->assertEquals(1, $queueService->getDispatchedJobsCount());
        
        // 测试Mock AI服务
        $aiServices = MockServices::createMockAiServices();
        $aiServices->setQualityAnalysisResult(['test' => 'result']);
        $result = $aiServices->getQualityAnalysisResult();
        $this->assertArrayHasKey('test', $result);
        $this->assertTrue($aiServices->wasQualityAnalysisCalled());
        
        $this->assertTrue(true, 'Mock services are working correctly');
    }
    
    /**
     * 测试AI SDR服务类存在性
     */
    public function testAiSdrClassesExist()
    {
        // 验证核心类存在
        $this->assertTrue(class_exists('\common\library\ai_sdr\AISdrService'), 'AISdrService class should exist');
        $this->assertTrue(class_exists('\common\library\ai_sdr\SdrDetailExecutor'), 'SdrDetailExecutor class should exist');
        $this->assertTrue(class_exists('\common\library\ai_sdr\Constant'), 'Constant class should exist');
        $this->assertTrue(class_exists('\common\library\ai_sdr\Helper'), 'Helper class should exist');
        
        // 验证模型类存在
        $this->assertTrue(class_exists('\common\library\ai_sdr\task\AiSdrTask'), 'AiSdrTask class should exist');
        $this->assertTrue(class_exists('\common\library\ai_sdr\task_detail\AiSdrTaskDetail'), 'AiSdrTaskDetail class should exist');
        $this->assertTrue(class_exists('\common\library\ai_sdr\task_record\AiSdrTaskRecord'), 'AiSdrTaskRecord class should exist');
        
        $this->assertTrue(true, 'All AI SDR classes exist');
    }

    /**
     * 测试AI质量分析结果数据结构
     */
    public function testQualityAnalysisResultStructure()
    {
        $result = AiSdrTestDataFactory::createQualityAnalysisResultData();
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('answer', $result);
        $this->assertArrayHasKey('usage', $result);
        $this->assertArrayHasKey('model', $result);
        
        $answer = $result['answer'];
        $this->assertArrayHasKey('lead_quality', $answer);
        $this->assertArrayHasKey('confidence', $answer);
        $this->assertArrayHasKey('reason', $answer);
        
        $this->assertTrue(true, 'Quality analysis result structure is correct');
    }
    
    /**
     * 测试背景调研结果数据结构
     */
    public function testBackgroundCheckResultStructure()
    {
        $result = AiSdrTestDataFactory::createBackgroundCheckResultData();
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('task_id', $result);
        $this->assertArrayHasKey('status', $result);
        $this->assertArrayHasKey('report', $result);
        $this->assertArrayHasKey('confidence', $result);
        
        $report = $result['report'];
        $this->assertArrayHasKey('company_name', $report);
        $this->assertArrayHasKey('country', $report);
        $this->assertArrayHasKey('homepage', $report);
        
        $this->assertTrue(true, 'Background check result structure is correct');
    }
    
    /**
     * 测试营销内容生成结果数据结构
     */
    public function testMarketingContentResultStructure()
    {
        $result = AiSdrTestDataFactory::createMarketingContentResultData();
        
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        
        $firstEmail = $result[0];
        $this->assertArrayHasKey('subject', $firstEmail);
        $this->assertArrayHasKey('content', $firstEmail);
        $this->assertArrayHasKey('round', $firstEmail);
        $this->assertArrayHasKey('tone', $firstEmail);
        $this->assertArrayHasKey('language', $firstEmail);
        
        $this->assertTrue(true, 'Marketing content result structure is correct');
    }
    
    /**
     * 测试推荐API响应数据结构
     */
    public function testRecommendApiResponseStructure()
    {
        $result = AiSdrTestDataFactory::createRecommendApiResponseData();
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('example.com', $result);
        
        $companyData = $result['example.com'];
        $this->assertArrayHasKey('company_name', $companyData);
        $this->assertArrayHasKey('main_products', $companyData);
        $this->assertArrayHasKey('company_type', $companyData);
        $this->assertArrayHasKey('country', $companyData);
        
        $this->assertTrue(true, 'Recommend API response structure is correct');
    }
    
    /**
     * 测试批量数据创建
     */
    public function testBatchDataCreation()
    {
        // 测试批量任务数据创建
        $tasks = AiSdrTestDataFactory::createMultipleTasksData(3);
        $this->assertCount(3, $tasks);
        $this->assertNotEquals($tasks[0]['email'], $tasks[1]['email']);
        
        // 测试批量详情数据创建
        $details = AiSdrTestDataFactory::createMultipleTaskDetailsData(12345, 2);
        $this->assertCount(2, $details);
        $this->assertEquals(12345, $details[0]['task_id']);
        $this->assertEquals(12345, $details[1]['task_id']);
        $this->assertNotEquals($details[0]['lead_id'], $details[1]['lead_id']);
        
        $this->assertTrue(true, 'Batch data creation is working correctly');
    }
}
