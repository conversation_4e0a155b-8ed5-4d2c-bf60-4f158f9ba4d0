#!/bin/bash

# AI Agent Mockery框架测试运行脚本
# 用于快速验证Mockery框架的功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="/Users/<USER>/dev/xiaoman/crm"
PHPUNIT_PATH="/opt/homebrew/bin/php /path/to/external/vendor/phpunit/phpunit/phpunit"
CONFIG_PATH="${PROJECT_ROOT}/protected/tests/phpunit.xml"

echo -e "${BLUE}=== AI Agent Mockery框架测试 ===${NC}"
echo "项目路径: ${PROJECT_ROOT}"
echo "配置文件: ${CONFIG_PATH}"
echo ""

# 检查必要文件
echo -e "${YELLOW}检查必要文件...${NC}"

REQUIRED_FILES=(
    "protected/tests/unit/ai_sdr/mockery/AiAgentFactoryMockery.php"
    "protected/tests/unit/ai_sdr/mockery/data/AgentResponseDataFactory.php"
    "protected/tests/unit/ai_sdr/mockery/traits/MockeryAgentTrait.php"
    "protected/tests/unit/ai_sdr/mockery/AiAgentMockeryIntegrationTest.php"
    "protected/tests/unit/ai_sdr/mockery/examples/AiAgentMockeryExampleTest.php"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "${PROJECT_ROOT}/${file}" ]; then
        echo -e "${GREEN}✓${NC} ${file}"
    else
        echo -e "${RED}✗${NC} ${file} - 文件不存在"
        exit 1
    fi
done

echo ""

# 检查Agent Mock文件
echo -e "${YELLOW}检查Agent Mock文件...${NC}"

AGENT_FILES=(
    "protected/tests/unit/ai_sdr/mockery/agents/MockSdrLeadQualityAnalysisAgent.php"
    "protected/tests/unit/ai_sdr/mockery/agents/MockSdrEdmWriteAgent.php"
    "protected/tests/unit/ai_sdr/mockery/agents/MockSdrSellerIndustryAnalyze.php"
    "protected/tests/unit/ai_sdr/mockery/agents/MockAiSdrSellerProfileAgent.php"
    "protected/tests/unit/ai_sdr/mockery/agents/MockAiSdrBuyerProfileAgent.php"
    "protected/tests/unit/ai_sdr/mockery/agents/MockHomepageAiAgent.php"
    "protected/tests/unit/ai_sdr/mockery/agents/MockAiSdrProductCategoryAiAgent.php"
    "protected/tests/unit/ai_sdr/mockery/agents/MockSdrHutchLeadQualityAgent.php"
)

for file in "${AGENT_FILES[@]}"; do
    if [ -f "${PROJECT_ROOT}/${file}" ]; then
        echo -e "${GREEN}✓${NC} $(basename ${file})"
    else
        echo -e "${RED}✗${NC} $(basename ${file}) - 文件不存在"
        exit 1
    fi
done

echo ""

# 运行测试函数
run_test() {
    local test_name="$1"
    local test_path="$2"
    local description="$3"
    
    echo -e "${BLUE}运行测试: ${test_name}${NC}"
    echo "描述: ${description}"
    echo "路径: ${test_path}"
    echo ""
    
    cd "${PROJECT_ROOT}"
    
    # 构建PHPUnit命令
    local cmd="${PHPUNIT_PATH} --configuration ${CONFIG_PATH} --verbose ${test_path}"
    
    echo "执行命令: ${cmd}"
    echo ""
    
    if eval "${cmd}"; then
        echo -e "${GREEN}✓ ${test_name} 测试通过${NC}"
        return 0
    else
        echo -e "${RED}✗ ${test_name} 测试失败${NC}"
        return 1
    fi
}

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 运行集成测试
echo -e "${YELLOW}=== 运行集成测试 ===${NC}"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "集成测试" "protected/tests/unit/ai_sdr/mockery/AiAgentMockeryIntegrationTest.php" "验证Mockery框架的完整功能"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

echo ""

# 运行示例测试
echo -e "${YELLOW}=== 运行示例测试 ===${NC}"
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "示例测试" "protected/tests/unit/ai_sdr/mockery/examples/AiAgentMockeryExampleTest.php" "展示Mockery框架的使用方法"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

echo ""

# 运行语法检查
echo -e "${YELLOW}=== 运行语法检查 ===${NC}"
echo "检查PHP语法错误..."

SYNTAX_ERRORS=0

for file in "${REQUIRED_FILES[@]}" "${AGENT_FILES[@]}"; do
    if php -l "${PROJECT_ROOT}/${file}" > /dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} $(basename ${file}) - 语法正确"
    else
        echo -e "${RED}✗${NC} $(basename ${file}) - 语法错误"
        SYNTAX_ERRORS=$((SYNTAX_ERRORS + 1))
    fi
done

echo ""

# 显示测试结果摘要
echo -e "${BLUE}=== 测试结果摘要 ===${NC}"
echo "总测试数: ${TOTAL_TESTS}"
echo -e "通过测试: ${GREEN}${PASSED_TESTS}${NC}"
echo -e "失败测试: ${RED}${FAILED_TESTS}${NC}"
echo -e "语法错误: ${RED}${SYNTAX_ERRORS}${NC}"

if [ ${FAILED_TESTS} -eq 0 ] && [ ${SYNTAX_ERRORS} -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 所有测试通过！AI Agent Mockery框架工作正常！${NC}"
    echo ""
    echo -e "${YELLOW}框架使用提示:${NC}"
    echo "1. 在测试类中使用 MockeryAgentTrait"
    echo "2. 在setUp()中调用 \$this->setUpAiAgentMockery()"
    echo "3. 在tearDown()中调用 \$this->tearDownAiAgentMockery()"
    echo "4. 使用预定义场景或自定义响应数据"
    echo "5. 验证Agent调用和参数"
    echo ""
    echo -e "${BLUE}查看完整使用指南:${NC}"
    echo "cat protected/tests/unit/ai_sdr/mockery/README.md"
    echo ""
    exit 0
else
    echo ""
    echo -e "${RED}❌ 测试失败！请检查错误信息并修复问题。${NC}"
    echo ""
    exit 1
fi
