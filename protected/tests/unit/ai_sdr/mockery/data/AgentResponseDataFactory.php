<?php

namespace tests\unit\ai_sdr\mockery\data;

use common\library\ai_sdr\Constant;

/**
 * AI Agent响应数据工厂
 * 
 * 为各种AI Agent提供标准化的测试响应数据
 */
class AgentResponseDataFactory
{
    /**
     * @var array 当前场景配置
     */
    private $currentScenario = [];
    
    /**
     * @var array 自定义响应数据
     */
    private $customResponses = [];

    /**
     * 设置测试场景
     */
    public function setScenario(string $scenarioName, array $config = []): void
    {
        $this->currentScenario = [
            'name' => $scenarioName,
            'config' => $config
        ];
    }

    /**
     * 设置自定义响应
     */
    public function setCustomResponse(int $sceneType, array $responseData): void
    {
        $this->customResponses[$sceneType] = $responseData;
    }

    /**
     * 获取质量分析Agent响应数据
     */
    public function getQualityAnalysisResponse(array $input = []): array
    {
        $sceneType = \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS;
        
        if (isset($this->customResponses[$sceneType])) {
            return $this->customResponses[$sceneType];
        }

        // 根据场景返回不同的质量等级
        $leadQuality = $this->getQualityByScenario();
        
        return [
            'answer' => [
                'lead_quality' => $leadQuality,
                'confidence' => $this->getRandomConfidence(),
                'reason' => $this->getQualityReasons($leadQuality),
                'score' => $this->getQualityScore($leadQuality)
            ],
            'record_id' => $this->generateRecordId()
        ];
    }

    /**
     * 获取EDM写作Agent响应数据
     */
    public function getEdmWriteResponse(array $input = []): array
    {
        $sceneType = \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER;
        
        if (isset($this->customResponses[$sceneType])) {
            return $this->customResponses[$sceneType];
        }

        return [
            [
                'subject' => $this->generateEmailSubject(),
                'content' => $this->generateEmailContent(),
                'round' => 1
            ],
            [
                'subject' => $this->generateEmailSubject(2),
                'content' => $this->generateEmailContent(2),
                'round' => 2
            ],
            [
                'subject' => $this->generateEmailSubject(3),
                'content' => $this->generateEmailContent(3),
                'round' => 3
            ]
        ];
    }

    /**
     * 获取卖家行业分析Agent响应数据
     */
    public function getSellerIndustryAnalysisResponse(array $input = []): array
    {
        $sceneType = \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_INDUSTRY_ANALYZE;
        
        if (isset($this->customResponses[$sceneType])) {
            return $this->customResponses[$sceneType];
        }

        return [
            'answer' => [
                [
                    'industry' => 'Technology',
                    'relative' => Constant::LEAD_QUALITY_HIGH
                ],
                [
                    'industry' => 'Manufacturing',
                    'relative' => Constant::LEAD_QUALITY_MEDIUM
                ],
                [
                    'industry' => 'Export Trade',
                    'relative' => Constant::LEAD_QUALITY_LOW
                ]
            ],
            'record_id' => $this->generateRecordId()
        ];
    }

    /**
     * 获取卖家档案Agent响应数据
     */
    public function getSellerProfileResponse(array $input = []): array
    {
        $sceneType = \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_PROFILE_GENERATION;
        
        if (isset($this->customResponses[$sceneType])) {
            return $this->customResponses[$sceneType];
        }

        return [
            'answer' => [
                'company_name' => 'Mock Technology Co., Ltd.',
                'established_year' => '2010',
                'scale' => ['100-500'],
                'types' => ['Manufacturer', 'Exporter'],
                'types_reason' => ['Based on product range and export activities'],
                'industry_type' => ['Technology', 'Electronics'],
                'industry_type_reason' => ['Primary business focus on tech products'],
                'public_homepage' => ['https://mocktech.com'],
                'primary_products' => ['Software Solutions', 'Hardware Components'],
                'products_category' => ['Technology → Software', 'Electronics → Components'],
                'country_code' => 'CN',
                'industry' => ['Information Technology'],
                'industry_reason' => ['Core business in IT sector'],
                'mode' => ['B2B', 'OEM'],
                'mode_reason' => ['Business model analysis'],
                'qualification' => ['ISO9001', 'CE Certification']
            ],
            'record_id' => $this->generateRecordId()
        ];
    }

    /**
     * 获取买家档案Agent响应数据
     */
    public function getBuyerProfileResponse(array $input = []): array
    {
        $sceneType = \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_BUYER_PROFILE_GENERATION;
        
        if (isset($this->customResponses[$sceneType])) {
            return $this->customResponses[$sceneType];
        }

        return [
            'answer' => [
                'company_name' => 'Mock Buyer Corp',
                'industry' => 'Retail',
                'company_type' => ['Distributor', 'Retailer'],
                'main_products' => ['Consumer Electronics', 'Home Appliances'],
                'target_market' => ['North America', 'Europe'],
                'purchasing_power' => 'High',
                'decision_makers' => ['Purchasing Manager', 'CEO'],
                'preferred_suppliers' => ['Certified manufacturers', 'Long-term partners'],
                'communication_preference' => 'Email',
                'business_scale' => 'Medium'
            ],
            'record_id' => $this->generateRecordId()
        ];
    }

    /**
     * 获取主页分析Agent响应数据
     */
    public function getHomepageAnalysisResponse(array $input = []): array
    {
        $sceneType = \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_HOMEPAGE_SEARCH;
        
        if (isset($this->customResponses[$sceneType])) {
            return $this->customResponses[$sceneType];
        }

        return [
            'answer' => [
                'homepage_analysis' => [
                    'company_info' => [
                        'name' => 'Mock Company from Homepage',
                        'description' => 'A leading technology company specializing in innovative solutions',
                        'contact_info' => [
                            'email' => '<EMAIL>',
                            'phone' => '******-0123',
                            'address' => '123 Tech Street, Silicon Valley, CA'
                        ]
                    ],
                    'products' => ['Product A', 'Product B', 'Product C'],
                    'services' => ['Service X', 'Service Y', 'Service Z'],
                    'certifications' => ['ISO9001', 'CE', 'FCC'],
                    'markets' => ['Global', 'B2B', 'B2C']
                ],
                'confidence' => 0.87
            ],
            'record_id' => $this->generateRecordId()
        ];
    }

    /**
     * 获取产品分类Agent响应数据
     */
    public function getProductCategoryResponse(array $input = []): array
    {
        $sceneType = \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_WASH_PRODUCT_CATEGORY;
        
        if (isset($this->customResponses[$sceneType])) {
            return $this->customResponses[$sceneType];
        }

        return [
            'answer' => [
                'categories' => [
                    'Electronics → Consumer Electronics → Mobile Phones',
                    'Technology → Software → Business Software',
                    'Manufacturing → Industrial Equipment → Automation'
                ],
                'confidence' => 0.88,
                'primary_category' => 'Electronics → Consumer Electronics',
                'secondary_categories' => [
                    'Technology → Software',
                    'Manufacturing → Industrial Equipment'
                ]
            ],
            'record_id' => $this->generateRecordId()
        ];
    }

    /**
     * 根据场景获取质量等级
     */
    private function getQualityByScenario(): string
    {
        $scenarioName = $this->currentScenario['name'] ?? '';
        $config = $this->currentScenario['config'] ?? [];
        
        if (isset($config['lead_quality'])) {
            return $config['lead_quality'];
        }
        
        switch ($scenarioName) {
            case 'high_quality_leads':
                return Constant::LEAD_QUALITY_HIGH;
            case 'medium_quality_leads':
                return Constant::LEAD_QUALITY_MEDIUM;
            case 'low_quality_leads':
                return Constant::LEAD_QUALITY_LOW;
            default:
                return Constant::LEAD_QUALITY_MEDIUM;
        }
    }

    /**
     * 获取随机置信度
     */
    private function getRandomConfidence(): float
    {
        return round(0.7 + (mt_rand() / mt_getrandmax()) * 0.3, 2);
    }

    /**
     * 获取质量原因
     */
    private function getQualityReasons(string $quality): array
    {
        switch ($quality) {
            case Constant::LEAD_QUALITY_HIGH:
                return ['高匹配度', '强购买意向', '决策权明确'];
            case Constant::LEAD_QUALITY_MEDIUM:
                return ['中等匹配度', '有潜在需求', '需要进一步沟通'];
            case Constant::LEAD_QUALITY_LOW:
                return ['匹配度较低', '需求不明确', '决策流程复杂'];
            default:
                return ['需要更多信息'];
        }
    }

    /**
     * 获取质量分数
     */
    private function getQualityScore(string $quality): int
    {
        switch ($quality) {
            case Constant::LEAD_QUALITY_HIGH:
                return mt_rand(80, 95);
            case Constant::LEAD_QUALITY_MEDIUM:
                return mt_rand(60, 79);
            case Constant::LEAD_QUALITY_LOW:
                return mt_rand(30, 59);
            default:
                return 50;
        }
    }

    /**
     * 生成邮件主题
     */
    private function generateEmailSubject(int $round = 1): string
    {
        $subjects = [
            1 => 'Partnership Opportunity - Quality Products for Your Market',
            2 => 'Follow-up: Exclusive Solutions for Your Business',
            3 => 'Final Offer: Premium Products at Competitive Prices'
        ];
        
        return $subjects[$round] ?? "Round {$round} - Business Collaboration Opportunity";
    }

    /**
     * 生成邮件内容
     */
    private function generateEmailContent(int $round = 1): string
    {
        $contents = [
            1 => "Dear Partner,\n\nWe are excited to introduce our premium product line that could be perfect for your market...",
            2 => "Hello again,\n\nI hope this email finds you well. Following up on our previous communication...",
            3 => "Dear Valued Partner,\n\nThis is our final outreach regarding the exclusive partnership opportunity..."
        ];
        
        return $contents[$round] ?? "Round {$round} email content for business collaboration.";
    }

    /**
     * 生成记录ID
     */
    private function generateRecordId(): int
    {
        return mt_rand(100000, 999999);
    }
}
