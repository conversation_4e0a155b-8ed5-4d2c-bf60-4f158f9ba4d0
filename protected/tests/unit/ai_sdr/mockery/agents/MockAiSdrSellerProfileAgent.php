<?php

namespace tests\unit\ai_sdr\mockery\agents;

use common\library\ai_agent\AiSdrSellerProfileAgent;
use common\library\ai_agent\AiAgentProcessResponse;
use tests\unit\ai_sdr\mockery\data\AgentResponseDataFactory;

/**
 * Mock AI SDR卖家档案Agent
 * 
 * 模拟AiSdrSellerProfileAgent的行为，用于测试
 */
class MockAiSdrSellerProfileAgent extends AiSdrSellerProfileAgent
{
    /**
     * @var AgentResponseDataFactory
     */
    private $dataFactory;
    
    /**
     * @var array 调用日志
     */
    private $callLog = [];
    
    public function __construct(int $clientId, int $userId, AgentResponseDataFactory $dataFactory)
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
        $this->dataFactory = $dataFactory;
        
        // 不调用父类构造函数，避免真实的初始化
    }

    /**
     * Mock process方法
     */
    public function process(array $params = [], string $function = '')
    {
        // 记录调用日志
        $this->callLog[] = [
            'method' => 'process',
            'params' => $params,
            'function' => $function,
            'timestamp' => time(),
            'clientId' => $this->clientId,
            'userId' => $this->userId
        ];

        // 根据function参数调用不同的处理方法
        if (empty($function)) {
            $function = "washSellerProfile";
        }
        
        if (method_exists($this, 'mock' . ucfirst($function))) {
            return $this->{'mock' . ucfirst($function)}($params);
        }
        
        return $this->mockWashSellerProfile($params);
    }

    /**
     * Mock washSellerProfile方法
     */
    protected function mockWashSellerProfile($params)
    {
        // 获取Mock响应数据
        $responseData = $this->dataFactory->getSellerProfileResponse($params);
        
        // 创建AiAgentProcessResponse对象
        $response = new AiAgentProcessResponse();
        $response->answer = $responseData['answer'];
        $response->recordId = $responseData['record_id'];
        $response->messageType = 'text';
        $response->function = 'washSellerProfile';
        
        return $response;
    }

    /**
     * Mock getAgentSceneType方法
     */
    public function getAgentSceneType(): int
    {
        return \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_PROFILE_GENERATION;
    }

    /**
     * Mock makeAgentProcessParams方法
     */
    public function makeAgentProcessParams(): array
    {
        return [
            'client_id' => $this->clientId,
            'user_id' => $this->userId,
            'scene_type' => $this->getAgentSceneType()
        ];
    }

    /**
     * 获取调用日志
     */
    public function getCallLog(): array
    {
        return $this->callLog;
    }

    /**
     * 清空调用日志
     */
    public function clearCallLog(): void
    {
        $this->callLog = [];
    }

    /**
     * 检查是否被调用过
     */
    public function wasCalled(): bool
    {
        return !empty($this->callLog);
    }

    /**
     * 获取调用次数
     */
    public function getCallCount(): int
    {
        return count($this->callLog);
    }

    /**
     * 获取最后一次调用的参数
     */
    public function getLastCallParams(): ?array
    {
        if (empty($this->callLog)) {
            return null;
        }
        
        return end($this->callLog)['params'];
    }

    /**
     * 验证调用参数
     */
    public function wasCalledWith(array $expectedParams): bool
    {
        foreach ($this->callLog as $call) {
            if ($this->paramsMatch($call['params'], $expectedParams)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 参数匹配检查
     */
    private function paramsMatch(array $actualParams, array $expectedParams): bool
    {
        foreach ($expectedParams as $key => $expectedValue) {
            if (!isset($actualParams[$key]) || $actualParams[$key] !== $expectedValue) {
                return false;
            }
        }
        return true;
    }

    /**
     * 设置自定义响应数据
     */
    public function setCustomResponse(array $responseData): void
    {
        $this->dataFactory->setCustomResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_PROFILE_GENERATION,
            $responseData
        );
    }

    /**
     * Mock isSdr方法
     */
    public function isSdr(): bool
    {
        return true;
    }

    /**
     * 获取客户端ID
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * 获取卖家档案信息
     */
    public function getSellerProfile(array $params = []): array
    {
        $response = $this->process($params, 'washSellerProfile');
        return $response->answer;
    }

    /**
     * 获取公司基本信息
     */
    public function getCompanyBasicInfo(array $params = []): array
    {
        $profile = $this->getSellerProfile($params);
        
        return [
            'company_name' => $profile['company_name'] ?? '',
            'established_year' => $profile['established_year'] ?? '',
            'scale' => $profile['scale'] ?? [],
            'country_code' => $profile['country_code'] ?? ''
        ];
    }

    /**
     * 获取产品信息
     */
    public function getProductInfo(array $params = []): array
    {
        $profile = $this->getSellerProfile($params);
        
        return [
            'primary_products' => $profile['primary_products'] ?? [],
            'products_category' => $profile['products_category'] ?? []
        ];
    }

    /**
     * 获取行业信息
     */
    public function getIndustryInfo(array $params = []): array
    {
        $profile = $this->getSellerProfile($params);
        
        return [
            'industry_type' => $profile['industry_type'] ?? [],
            'industry' => $profile['industry'] ?? [],
            'industry_reason' => $profile['industry_reason'] ?? []
        ];
    }

    /**
     * 获取公司类型
     */
    public function getCompanyTypes(array $params = []): array
    {
        $profile = $this->getSellerProfile($params);
        return $profile['types'] ?? [];
    }

    /**
     * 获取合作模式
     */
    public function getCooperationModes(array $params = []): array
    {
        $profile = $this->getSellerProfile($params);
        return $profile['mode'] ?? [];
    }

    /**
     * 验证必要参数
     */
    public function validateRequiredParams(array $params): bool
    {
        $requiredKeys = ['company_name'];
        
        foreach ($requiredKeys as $key) {
            if (!isset($params[$key]) || empty($params[$key])) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 设置卖家档案场景
     */
    public function setSellerProfileScenario(string $scenario): void
    {
        switch ($scenario) {
            case 'tech_manufacturer':
                $this->setCustomResponse([
                    'answer' => [
                        'company_name' => 'TechCorp Manufacturing Ltd.',
                        'established_year' => '2005',
                        'scale' => ['500-1000'],
                        'types' => ['Manufacturer', 'Exporter', 'R&D'],
                        'industry_type' => ['Technology', 'Electronics'],
                        'primary_products' => ['Smart Devices', 'IoT Solutions'],
                        'products_category' => ['Technology → IoT', 'Electronics → Smart Devices'],
                        'country_code' => 'CN',
                        'mode' => ['B2B', 'OEM', 'ODM'],
                        'qualification' => ['ISO9001', 'CE', 'FCC', 'RoHS']
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
                
            case 'trading_company':
                $this->setCustomResponse([
                    'answer' => [
                        'company_name' => 'Global Trade Solutions Co.',
                        'established_year' => '2010',
                        'scale' => ['50-100'],
                        'types' => ['Trading Company', 'Exporter'],
                        'industry_type' => ['Trade', 'Export'],
                        'primary_products' => ['Various Products', 'Sourcing Services'],
                        'products_category' => ['Trade → General', 'Services → Sourcing'],
                        'country_code' => 'CN',
                        'mode' => ['B2B', 'Trading'],
                        'qualification' => ['Export License', 'ISO9001']
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
                
            case 'startup_company':
                $this->setCustomResponse([
                    'answer' => [
                        'company_name' => 'Innovation Startup Inc.',
                        'established_year' => '2020',
                        'scale' => ['10-50'],
                        'types' => ['Startup', 'Technology'],
                        'industry_type' => ['Technology', 'Innovation'],
                        'primary_products' => ['Software Solutions', 'Mobile Apps'],
                        'products_category' => ['Technology → Software', 'Technology → Mobile'],
                        'country_code' => 'US',
                        'mode' => ['B2B', 'SaaS'],
                        'qualification' => ['Pending']
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
        }
    }

    /**
     * 模拟异常情况
     */
    public function simulateException(string $message = 'Mock Seller Profile Agent Exception'): void
    {
        $this->dataFactory->setCustomResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_PROFILE_GENERATION,
            ['exception' => $message]
        );
    }

    /**
     * 重写process方法以支持异常模拟
     */
    public function processWithExceptionHandling(array $params = [], string $function = '')
    {
        try {
            $responseData = $this->dataFactory->getSellerProfileResponse($params);
            
            // 检查是否需要抛出异常
            if (isset($responseData['exception'])) {
                throw new \common\library\ai_agent\AiAgentException($responseData['exception']);
            }
            
            return $this->process($params, $function);
        } catch (\Exception $e) {
            $this->callLog[] = [
                'method' => 'process',
                'params' => $params,
                'function' => $function,
                'exception' => $e->getMessage(),
                'timestamp' => time()
            ];
            throw $e;
        }
    }
}
