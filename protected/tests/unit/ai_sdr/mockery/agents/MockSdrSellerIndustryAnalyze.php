<?php

namespace tests\unit\ai_sdr\mockery\agents;

use common\library\ai_agent\SdrSellerIndustryAnalyze;
use common\library\ai_agent\AiAgentProcessResponse;
use tests\unit\ai_sdr\mockery\data\AgentResponseDataFactory;

/**
 * Mock SDR卖家行业分析Agent
 * 
 * 模拟SdrSellerIndustryAnalyze的行为，用于测试
 */
class MockSdrSellerIndustryAnalyze extends SdrSellerIndustryAnalyze
{
    /**
     * @var AgentResponseDataFactory
     */
    private $dataFactory;
    
    /**
     * @var array 调用日志
     */
    private $callLog = [];

    public function __construct(int $clientId, int $userId, AgentResponseDataFactory $dataFactory)
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
        $this->dataFactory = $dataFactory;
        
        // 不调用父类构造函数，避免真实的初始化
    }

    /**
     * Mock process方法
     */
    public function process(array $params = [], string $function = ''):array
    {
        // 记录调用日志
        $this->callLog[] = [
            'method' => 'process',
            'params' => $params,
            'function' => $function,
            'timestamp' => time(),
            'clientId' => $this->clientId,
            'userId' => $this->userId
        ];

        // 获取Mock响应数据
        $responseData = $this->dataFactory->getSellerIndustryAnalysisResponse($params);
        
        // 模拟formatAnswer处理
        $formattedData = $this->formatAnswer($responseData['answer']);
        
        // 创建AiAgentProcessResponse对象
        $response = new AiAgentProcessResponse();
        $response->answer = $formattedData;
        $response->recordId = $responseData['record_id'];
        $response->messageType = 'text';
        $response->function = $function ?: 'sellerIndustryAnalysis';
        
        return $response;
    }

    /**
     * Mock getAgentSceneType方法
     */
    public function getAgentSceneType(): int
    {
        return \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_INDUSTRY_ANALYZE;
    }

    /**
     * Mock formatAnswer方法
     */
    protected function formatAnswer(array $answer): array
    {
        // 模拟原始的格式化逻辑
        foreach ($answer as &$industryUsage) {
            $relative = $industryUsage['relative'] ?? 0;
            if ($relative > 0.6) {
                $relativeFlag = \common\library\ai_sdr\Constant::LEAD_QUALITY_HIGH;
            } else if ($relative > 0.3) {
                $relativeFlag = \common\library\ai_sdr\Constant::LEAD_QUALITY_MEDIUM;
            } else if ($relative > 0) {
                $relativeFlag = \common\library\ai_sdr\Constant::LEAD_QUALITY_LOW;
            } else {
                $relativeFlag = \common\library\ai_sdr\Constant::LEAD_QUALITY_UNKNOWN;
            }
            $industryUsage['relative'] = $relativeFlag;
        }
        return $answer;
    }

    /**
     * 获取调用日志
     */
    public function getCallLog(): array
    {
        return $this->callLog;
    }

    /**
     * 清空调用日志
     */
    public function clearCallLog(): void
    {
        $this->callLog = [];
    }

    /**
     * 检查是否被调用过
     */
    public function wasCalled(): bool
    {
        return !empty($this->callLog);
    }

    /**
     * 获取调用次数
     */
    public function getCallCount(): int
    {
        return count($this->callLog);
    }

    /**
     * 获取最后一次调用的参数
     */
    public function getLastCallParams(): ?array
    {
        if (empty($this->callLog)) {
            return null;
        }
        
        return end($this->callLog)['params'];
    }

    /**
     * 验证调用参数
     */
    public function wasCalledWith(array $expectedParams): bool
    {
        foreach ($this->callLog as $call) {
            if ($this->paramsMatch($call['params'], $expectedParams)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 参数匹配检查
     */
    private function paramsMatch(array $actualParams, array $expectedParams): bool
    {
        foreach ($expectedParams as $key => $expectedValue) {
            if (!isset($actualParams[$key]) || $actualParams[$key] !== $expectedValue) {
                return false;
            }
        }
        return true;
    }

    /**
     * 设置自定义响应数据
     */
    public function setCustomResponse(array $responseData): void
    {
        $this->dataFactory->setCustomResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_INDUSTRY_ANALYZE,
            $responseData
        );
    }

    /**
     * Mock isSdr方法
     */
    public function isSdr(): bool
    {
        return true;
    }

    /**
     * Mock makeAgentProcessParams方法
     */
    public function makeAgentProcessParams(): array
    {
        return [
            'client_id' => $this->clientId,
            'user_id' => $this->userId,
            'scene_type' => $this->getAgentSceneType()
        ];
    }

    /**
     * 获取客户端ID
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * 获取行业分析结果
     */
    public function getIndustryAnalysisResults(array $params = []): array
    {
        $response = $this->process($params);
        return $response->answer;
    }

    /**
     * 获取高质量行业
     */
    public function getHighQualityIndustries(array $params = []): array
    {
        $results = $this->getIndustryAnalysisResults($params);
        
        return array_filter($results, function($industry) {
            return $industry['relative'] === \common\library\ai_sdr\Constant::LEAD_QUALITY_HIGH;
        });
    }

    /**
     * 获取主要行业
     */
    public function getPrimaryIndustry(array $params = []): ?string
    {
        $results = $this->getIndustryAnalysisResults($params);
        
        if (empty($results)) {
            return null;
        }
        
        // 返回第一个高质量行业，如果没有则返回第一个
        $highQualityIndustries = $this->getHighQualityIndustries($params);
        if (!empty($highQualityIndustries)) {
            return reset($highQualityIndustries)['industry'];
        }
        
        return $results[0]['industry'] ?? null;
    }

    /**
     * 验证行业匹配度
     */
    public function validateIndustryMatch(string $targetIndustry, array $params = []): bool
    {
        $results = $this->getIndustryAnalysisResults($params);
        
        foreach ($results as $industry) {
            if (stripos($industry['industry'], $targetIndustry) !== false) {
                return $industry['relative'] !== \common\library\ai_sdr\Constant::LEAD_QUALITY_LOW;
            }
        }
        
        return false;
    }

    /**
     * 模拟异常情况
     */
    public function simulateException(string $message = 'Mock Seller Industry Analyze Exception'): void
    {
        $this->dataFactory->setCustomResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_INDUSTRY_ANALYZE,
            ['exception' => $message]
        );
    }

    /**
     * 重写process方法以支持异常模拟
     */
    public function processWithExceptionHandling(array $params = [], string $function = '')
    {
        try {
            $responseData = $this->dataFactory->getSellerIndustryAnalysisResponse($params);
            
            // 检查是否需要抛出异常
            if (isset($responseData['exception'])) {
                throw new \common\library\ai_agent\AiAgentException($responseData['exception']);
            }
            
            return $this->process($params, $function);
        } catch (\Exception $e) {
            $this->callLog[] = [
                'method' => 'process',
                'params' => $params,
                'function' => $function,
                'exception' => $e->getMessage(),
                'timestamp' => time()
            ];
            throw $e;
        }
    }

    /**
     * 设置行业分析场景
     */
    public function setIndustryScenario(string $scenario): void
    {
        switch ($scenario) {
            case 'high_tech_match':
                $this->setCustomResponse([
                    'answer' => [
                        [
                            'industry' => 'Technology',
                            'relative' => \common\library\ai_sdr\Constant::LEAD_QUALITY_HIGH
                        ],
                        [
                            'industry' => 'Software',
                            'relative' => \common\library\ai_sdr\Constant::LEAD_QUALITY_HIGH
                        ]
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
                
            case 'mixed_industries':
                $this->setCustomResponse([
                    'answer' => [
                        [
                            'industry' => 'Technology',
                            'relative' => \common\library\ai_sdr\Constant::LEAD_QUALITY_HIGH
                        ],
                        [
                            'industry' => 'Manufacturing',
                            'relative' => \common\library\ai_sdr\Constant::LEAD_QUALITY_MEDIUM
                        ],
                        [
                            'industry' => 'Agriculture',
                            'relative' => \common\library\ai_sdr\Constant::LEAD_QUALITY_LOW
                        ]
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
                
            case 'no_match':
                $this->setCustomResponse([
                    'answer' => [
                        [
                            'industry' => 'Unknown',
                            'relative' => \common\library\ai_sdr\Constant::LEAD_QUALITY_UNKNOWN
                        ]
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
        }
    }
}
