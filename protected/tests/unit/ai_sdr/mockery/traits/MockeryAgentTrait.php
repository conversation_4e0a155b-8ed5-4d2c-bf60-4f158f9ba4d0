<?php

namespace tests\unit\ai_sdr\mockery\traits;

use tests\unit\ai_sdr\mockery\AiAgentFactoryMockery;

/**
 * Mockery Agent测试辅助Trait
 * 
 * 为测试类提供便捷的AI Agent Mock功能
 */
trait MockeryAgentTrait
{
    /**
     * @var bool 是否已初始化Mockery
     */
    private static $mockeryInitialized = false;

    /**
     * 设置AI Agent Mockery环境
     */
    protected function setUpAiAgentMockery(): void
    {
        if (!self::$mockeryInitialized) {
            AiAgentFactoryMockery::initialize();
            self::$mockeryInitialized = true;
        }
    }

    /**
     * 清理AI Agent Mockery环境
     */
    protected function tearDownAiAgentMockery(): void
    {
        AiAgentFactoryMockery::cleanup();
        self::$mockeryInitialized = false;
    }

    /**
     * 重置AI Agent Mockery状态
     */
    protected function resetAiAgentMockery(): void
    {
        AiAgentFactoryMockery::reset();
    }

    /**
     * 设置测试场景
     */
    protected function setAiAgentTestScenario(string $scenarioName, array $config = []): void
    {
        AiAgentFactoryMockery::setTestScenario($scenarioName, $config);
    }

    /**
     * 设置特定Agent的响应数据
     */
    protected function setAgentResponse(int $sceneType, array $responseData): void
    {
        AiAgentFactoryMockery::setAgentResponse($sceneType, $responseData);
    }

    /**
     * 验证Agent是否被调用
     */
    protected function assertAgentWasCalled(int $sceneType, string $message = ''): void
    {
        $wasCalled = AiAgentFactoryMockery::wasAgentCalled($sceneType);
        $this->assertTrue($wasCalled, $message ?: "Agent with scene type {$sceneType} was not called");
    }

    /**
     * 验证Agent没有被调用
     */
    protected function assertAgentWasNotCalled(int $sceneType, string $message = ''): void
    {
        $wasCalled = AiAgentFactoryMockery::wasAgentCalled($sceneType);
        $this->assertFalse($wasCalled, $message ?: "Agent with scene type {$sceneType} was called unexpectedly");
    }

    /**
     * 验证Agent调用次数
     */
    protected function assertAgentCallCount(int $sceneType, int $expectedCount, string $message = ''): void
    {
        $actualCount = AiAgentFactoryMockery::getAgentCallCount($sceneType);
        $this->assertEquals($expectedCount, $actualCount, $message ?: "Agent call count mismatch for scene type {$sceneType}");
    }

    /**
     * 验证Agent调用参数
     */
    protected function assertAgentCalledWith(int $sceneType, array $expectedParams, string $message = ''): void
    {
        $lastCall = AiAgentFactoryMockery::getLastAgentCallParams($sceneType);
        $this->assertNotNull($lastCall, "Agent with scene type {$sceneType} was not called");
        
        foreach ($expectedParams as $key => $expectedValue) {
            $this->assertArrayHasKey($key, $lastCall, "Parameter '{$key}' not found in agent call");
            $this->assertEquals($expectedValue, $lastCall[$key], $message ?: "Parameter '{$key}' value mismatch");
        }
    }

    /**
     * 获取Agent调用日志
     */
    protected function getAgentCallLog(): array
    {
        return AiAgentFactoryMockery::getCallLog();
    }

    /**
     * 清空Agent调用日志
     */
    protected function clearAgentCallLog(): void
    {
        AiAgentFactoryMockery::clearCallLog();
    }

    /**
     * 设置质量分析Agent场景
     */
    protected function setQualityAnalysisScenario(string $scenario): void
    {
        switch ($scenario) {
            case 'high_quality':
                $this->setAgentResponse(\AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS, [
                    'answer' => [
                        'lead_quality' => \common\library\ai_sdr\Constant::LEAD_QUALITY_HIGH,
                        'confidence' => 0.92,
                        'reason' => ['高匹配度', '强购买意向', '决策权明确'],
                        'score' => 88
                    ],
                    'record_id' => 123456
                ]);
                break;
                
            case 'medium_quality':
                $this->setAgentResponse(\AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS, [
                    'answer' => [
                        'lead_quality' => \common\library\ai_sdr\Constant::LEAD_QUALITY_MEDIUM,
                        'confidence' => 0.75,
                        'reason' => ['中等匹配度', '有潜在需求', '需要进一步沟通'],
                        'score' => 65
                    ],
                    'record_id' => 123456
                ]);
                break;
                
            case 'low_quality':
                $this->setAgentResponse(\AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS, [
                    'answer' => [
                        'lead_quality' => \common\library\ai_sdr\Constant::LEAD_QUALITY_LOW,
                        'confidence' => 0.45,
                        'reason' => ['匹配度较低', '需求不明确', '决策流程复杂'],
                        'score' => 35
                    ],
                    'record_id' => 123456
                ]);
                break;
        }
    }

    /**
     * 设置EDM写作Agent场景
     */
    protected function setEdmWriteScenario(string $scenario): void
    {
        switch ($scenario) {
            case 'standard_emails':
                $this->setAgentResponse(\AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER, [
                    [
                        'subject' => 'Partnership Opportunity - Quality Products for Your Market',
                        'content' => "Dear Partner,\n\nWe are excited to introduce our premium product line...",
                        'round' => 1
                    ],
                    [
                        'subject' => 'Follow-up: Exclusive Solutions for Your Business',
                        'content' => "Hello again,\n\nI hope this email finds you well...",
                        'round' => 2
                    ],
                    [
                        'subject' => 'Final Offer: Premium Products at Competitive Prices',
                        'content' => "Dear Valued Partner,\n\nThis is our final outreach...",
                        'round' => 3
                    ]
                ]);
                break;
                
            case 'single_email':
                $this->setAgentResponse(\AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER, [
                    [
                        'subject' => 'Business Collaboration Opportunity',
                        'content' => "Dear Business Partner,\n\nWe would like to explore collaboration opportunities...",
                        'round' => 1
                    ]
                ]);
                break;
        }
    }

    /**
     * 设置卖家行业分析Agent场景
     */
    protected function setSellerIndustryAnalysisScenario(string $scenario): void
    {
        switch ($scenario) {
            case 'tech_focused':
                $this->setAgentResponse(\AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_INDUSTRY_ANALYZE, [
                    'answer' => [
                        [
                            'industry' => 'Technology',
                            'relative' => \common\library\ai_sdr\Constant::LEAD_QUALITY_HIGH
                        ],
                        [
                            'industry' => 'Software',
                            'relative' => \common\library\ai_sdr\Constant::LEAD_QUALITY_HIGH
                        ]
                    ],
                    'record_id' => 123456
                ]);
                break;
                
            case 'mixed_industries':
                $this->setAgentResponse(\AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_INDUSTRY_ANALYZE, [
                    'answer' => [
                        [
                            'industry' => 'Technology',
                            'relative' => \common\library\ai_sdr\Constant::LEAD_QUALITY_HIGH
                        ],
                        [
                            'industry' => 'Manufacturing',
                            'relative' => \common\library\ai_sdr\Constant::LEAD_QUALITY_MEDIUM
                        ],
                        [
                            'industry' => 'Agriculture',
                            'relative' => \common\library\ai_sdr\Constant::LEAD_QUALITY_LOW
                        ]
                    ],
                    'record_id' => 123456
                ]);
                break;
        }
    }

    /**
     * 模拟Agent异常
     */
    protected function simulateAgentException(int $sceneType, string $message = 'Mock Agent Exception'): void
    {
        $this->setAgentResponse($sceneType, ['exception' => $message]);
    }

    /**
     * 验证所有预期的Agent都被调用了
     */
    protected function assertAllExpectedAgentsCalled(array $expectedSceneTypes, string $message = ''): void
    {
        foreach ($expectedSceneTypes as $sceneType) {
            $this->assertAgentWasCalled($sceneType, $message);
        }
    }

    /**
     * 验证只有预期的Agent被调用了
     */
    protected function assertOnlyExpectedAgentsCalled(array $expectedSceneTypes, string $message = ''): void
    {
        $callLog = $this->getAgentCallLog();
        $actualSceneTypes = array_unique(array_column($callLog, 'sceneType'));
        
        sort($expectedSceneTypes);
        sort($actualSceneTypes);
        
        $this->assertEquals($expectedSceneTypes, $actualSceneTypes, $message ?: 'Unexpected agents were called');
    }

    /**
     * 获取Agent调用统计
     */
    protected function getAgentCallStatistics(): array
    {
        $callLog = $this->getAgentCallLog();
        $statistics = [];
        
        foreach ($callLog as $call) {
            $sceneType = $call['sceneType'];
            if (!isset($statistics[$sceneType])) {
                $statistics[$sceneType] = 0;
            }
            $statistics[$sceneType]++;
        }
        
        return $statistics;
    }

    /**
     * 打印Agent调用统计（用于调试）
     */
    protected function printAgentCallStatistics(): void
    {
        $statistics = $this->getAgentCallStatistics();
        echo "\n=== Agent Call Statistics ===\n";
        foreach ($statistics as $sceneType => $count) {
            echo "Scene Type {$sceneType}: {$count} calls\n";
        }
        echo "=============================\n";
    }
}
